import BtnAction from "@/components/common/BtnAction";
import ListSlider from "@/components/home/<USER>";
import NttShareIcon from "@/components/icon/NttShareIcon";
import ProductItem, { getMinPriceInListVariant } from "@/components/products/item/ProductItemFnB";
import NoDataView from "@/components/UI/NoDataView";
import { Router } from "@/constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import {
  clearProductSameCategory,
  getProductListSameCategory,
  setSearchCondition,
} from "@/redux/slices/product/productListSlice";
import { formatNumberToK, showToast } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { Box, Button, Divider, IconButton, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { openShareSheet } from "zmp-sdk/apis";
import { ItemGallery } from "../components/ItemGallery";
import FrameContainerFull from "../components/layout/ContainerFluid";
import BottomTabNavigationDetail from "../components/menu/bottomtabnav";
import { COLORS } from "../constants/themes";
import { clearProductDetail, getProductDetail } from "../redux/slices/product/productSlice";
import { AppDispatch, RootState } from "../redux/store";
import { IProduct } from "../types/product";
import { formatPrice } from "../utils/formatPrice";
export default function ProductDetail() {
  const { productDetail, isLoading } = useSelector((state: RootState) => state.product);
  const { hotProductList, list, productSameCategory } = useSelector(
    (state: RootState) => state.productList
  );

  const [localProductSameCategory, setLocalProductSameCategory] = useState<IProduct[]>([]);
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useParams();
  const appConfig = useConfigApp();

  const [product, setProduct] = useState<IProduct>();
  const { color, home } = useConfigApp();
  const navigate = useNavigate();
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(false);
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);
  const soldConfig = home?.find(
    (item) => item.type === "ProductList2" && item.style?.sold !== undefined
  )?.style?.sold;
  useEffect(() => {
    if (!productDetail) return;

    setProduct(productDetail);
    productDetail.categoryIds?.length &&
      dispatch(
        getProductListSameCategory({
          categoryId: productDetail.categoryIds[0],
        })
      );
  }, [productDetail, id]);

  useEffect(() => {
    if (!id) return;

    dispatch(getProductDetail(id));
  }, [id]);

  useEffect(() => {
    return () => {
      dispatch(clearProductDetail());
      dispatch(clearProductSameCategory());
    };
  }, []);
  useEffect(() => {
    if (productDetail && productSameCategory.length) {
      const productRelated = productSameCategory.filter(
        (product) => product.itemsCode !== productDetail?.itemsCode
      );
      setLocalProductSameCategory(productRelated);
    }
  }, [productSameCategory, productDetail]);

  const getMinPriceInListVariant = (variants) => {
    if (!variants || variants.length === 0) return 0;
    return Math.min(...variants.map((variant) => variant.price));
  };

  const salePrice = product?.price || getMinPriceInListVariant(product?.listVariant) || 0;
  const priceReal =
    product?.priceReal ||
    getMinPriceInListVariant(product?.listVariant.map((v) => ({ ...v, price: v.priceReal }))) ||
    0;

  const rawDiscount =
    priceReal > salePrice && priceReal > 0 ? ((priceReal - salePrice) / priceReal) * 100 : 0;

  const discountPercent =
    priceReal > 0 ? Math.round(((priceReal - salePrice) / priceReal) * 100) : 0;

  const onShareCurrentPage = async (title, description, thumbnail, path) => {
    try {
      let sharedObj = {
        title,
        description,
        thumbnail,
      };
      if (path) {
        sharedObj = Object.assign(sharedObj, { path });
      }
      const data = await openShareSheet({
        type: "zmp_deep_link",
        data: sharedObj,
      });
    } catch (err) {
      console.log("onShareCurrentPage error", err);
    }
  };
  const handleInfoClick = () => {
    showToast({
      content: "Tính năng đang phát triển",
      type: "blank",
    });
  };

  const handleShareClick = () => {
    showToast({
      content: "Tính năng đang phát triển",
      type: "blank",
    });
  };

  const discountType = home?.find((h) => h.style?.discountType)?.style?.discountType;
  const handleBack = () => {
    if (productDetail && productDetail.categoryIds && productDetail.categoryIds.length > 0) {
      navigate(Router.menu, { state: { categoryId: productDetail.categoryIds[0] } });
    } else {
      navigate(Router.menu);
    }
  };
  return (
    <FrameContainerFull
      title={"Chi tiết sản phẩm"}
      overrideStyle={{ background: "transparent" }}
    >
      <Box sx={{ background: COLORS.bgColor.fourth, position: "relative" }}>
        <Box sx={{ position: "absolute", bottom: 66, right: 0, zIndex: 10 }}>
          <Stack direction="row" alignItems="stretch">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                background: "#bbd5e0",
                padding: "4px 8px",
                borderRadius: "8px 0 0 8px",
                mb: 2,
              }}
            >
              <Typography
                sx={{
                  fontWeight: 500,
                  fontSize: "10px",
                }}
              >
                Chia sẻ để nhận thưởng
              </Typography>
              <IconButton
                size="small"
                onClick={handleInfoClick}
                sx={{
                  color: "#666",
                  padding: "4px",
                }}
              >
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </Box>
            <Stack
              bgcolor="#e5e5e5"
              p={1}
              sx={{ borderRadius: "0 8px 8px 0" }}
              onClick={handleShareClick}
              mb={2}
            >
              <NttShareIcon fillColor={appConfig.color.primary} />
            </Stack>
          </Stack>
        </Box>
        <ItemGallery
          images={
            productDetail?.images && productDetail.images.length > 0
              ? productDetail.images
              : [appConfig?.shopLogo || "/images/logo.png"]
          }
        />
      </Box>
      {product && (
        <>
          {/* product detail */}
          <Box style={styles.contentContainer}>
            {soldConfig !== false && (
              <Typography
                sx={{
                  color: "#bdbdbd",
                  fontSize: 15,
                  fontWeight: 400,
                  mb: 0.5,
                }}
              >
                Đã bán {formatNumberToK(product?.sold)}
              </Typography>
            )}
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-end",
                flexWrap: "wrap",
                gap: 1,
                minWidth: 0,
              }}
            >
              <Typography
                sx={{
                  color: color.primary,
                  fontWeight: 700,
                  fontSize: 22,
                  lineHeight: 1,
                  whiteSpace: "wrap",
                  minWidth: 0,
                  mb: 1,
                }}
              >
                {product?.itemsName}
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-end",
                flexWrap: "wrap",
                gap: 1,
                minWidth: 0,
              }}
            >
              <Typography
                sx={{
                  fontWeight: 500,
                  fontSize: 20,
                  lineHeight: 1.5,
                  whiteSpace: "nowrap",
                  minWidth: 0,
                }}
              >
                {formatPrice(salePrice)}
              </Typography>
              {priceReal !== salePrice && discountPercent > 0 && (
                <Typography
                  sx={{
                    width: 62,
                    height: 22,
                    backgroundColor: "rgba(255, 0, 0, 0.2)",
                    color: "red",
                    fontFamily: "SF Pro",
                    fontWeight: 510,
                    fontStyle: "normal",
                    fontSize: 10,
                    borderRadius: "100px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    lineHeight: "100%",
                    marginBottom: 0.5,
                  }}
                >
                  {`Giảm ${discountPercent}%`}
                </Typography>
              )}
              {priceReal !== salePrice && (
                <Typography
                  sx={{
                    color: "#c7c7c7",
                    fontSize: 14,
                    fontWeight: 400,
                    textDecoration: "line-through",
                    lineHeight: 1,
                    whiteSpace: "nowrap",
                    minWidth: 0,
                    flexBasis: "100%",
                  }}
                >
                  {formatPrice(priceReal)}
                </Typography>
              )}
            </Box>
          </Box>
          {product.itemsInfo && (
            <ProductDescription
              itemsInfo={product.itemsInfo}
              isExpanded={isExpanded}
              setIsExpanded={setIsExpanded}
              styles={styles}
            />
          )}
          {/* comments */}

          <Box
            style={{
              ...styles.contentContainer,
              marginTop: 8,
              marginBottom: shopInfo?.businessType === "Retail" ? 35 : undefined,
            }}
          >
            <ListSlider
              title={"Sản phẩm tương tự"}
              titleStyle={{ fontWeight: "500" }}
              btnAction={
                <BtnAction
                  styleOverride={{ color: color.primary, fontSize: 14, fontWeight: 400 }}
                  text={`Xem tất cả >`}
                  eventAction={() => {
                    if (
                      productDetail &&
                      productDetail.categoryIds &&
                      productDetail.categoryIds.length > 0
                    ) {
                      dispatch(
                        setSearchCondition({
                          categoryId: productDetail?.categoryIds[0],
                        })
                      );
                      navigate(Router.menu, {
                        state: { categoryId: productDetail?.categoryIds[0] },
                      });
                    }
                  }}
                />
              }
              seeAll={() => navigate(Router.menu)}
              sliceConfig={{
                dots: false,
                infinite: false,
                slidesToShow: 2,
                autoplay: false,
                arrows: false,
              }}
              overrideStyle={{ marginInline: -8 }}
            >
              {Array.isArray(localProductSameCategory) && localProductSameCategory.length > 0 ? (
                localProductSameCategory.map((item: IProduct) => (
                  <Box key={item.itemsCode} className="box-product-item">
                    <ProductItem item={item} discountType={discountType} />
                  </Box>
                ))
              ) : (
                <NoDataView content="Không có sản phẩm nào" />
              )}
            </ListSlider>
          </Box>
          <BottomTabNavigationDetail product={product} />
        </>
      )}
    </FrameContainerFull>
  );
}

const ProductDescription = ({ itemsInfo, isExpanded, setIsExpanded, styles }) => {
  const textForLineCount = itemsInfo
    .replace(/<li[^>]*>/gi, "\n")
    .replace(/<br\s*\/?\s*>/gi, "\n")
    .replace(/<p[^>]*>/gi, "\n")
    .replace(/<\/li>|<\/p>/gi, "")
    .replace(/<[^>]+>/g, "")
    .replace(/\n+/g, "\n")
    .trim();
  const lineCount = textForLineCount.split("\n").length;
  const plainText = textForLineCount;
  const isLong = plainText.length > 200 || lineCount > 5;
  const { color } = useConfigApp();
  return (
    <Stack style={{ ...styles.contentContainer, marginTop: 8, marginBottom: 8 }}>
      <Typography fontSize={17} fontWeight={500} style={styles.commentHeaderTitle}>
        Mô tả sản phẩm
      </Typography>
      <Box>
        <Box
          dangerouslySetInnerHTML={{
            __html:
              isExpanded || !isLong
                ? itemsInfo
                : `<div style='display: -webkit-box; -webkit-line-clamp: 5; -webkit-box-orient: vertical; overflow: hidden;'>${itemsInfo}</div>`,
          }}
          sx={{
            width: "100%",
            overflowX: "visible",
            "& *": {
              whiteSpace: "normal !important",
              wordBreak: "break-word !important",
              overflowWrap: "break-word !important",
              maxWidth: "100% !important",
            },
          }}
          mb={1}
        />
        <Divider />
        {isLong && (
          <Box textAlign="center">
            <Button
              variant="text"
              onClick={() => setIsExpanded((v) => !v)}
              sx={{ color: "#8B8B8B", fontSize: 14, fontWeight: 400 }}
            >
              {isExpanded ? "Thu gọn" : "Xem thêm"}{" "}
              {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </Button>
          </Box>
        )}
      </Box>
    </Stack>
  );
};

const styles: Record<string, React.CSSProperties> = {
  headerContainer: {
    display: "flex",
    alignItems: "baseline",
    justifyContent: "space-between",
  },
  rowContainer: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  contentContainer: {
    background: "#fff",
    paddingInline: 16,
    paddingBlock: 8,
  },
  finalPriceText: {
    color: COLORS.primary1,
    fontWeight: 700,
    fontStyle: "italic",
    fontSize: 20,
  },
  discountPercentText: {
    color: COLORS.primary1,
    fontStyle: "italic",
    marginInline: 4,
  },
  priceText: {
    color: COLORS.neutral4,
    display: "inline-block",
    fontStyle: "italic",
    paddingInline: 2,
  },
  ratingInfo: {
    alignItems: "center",
  },
  commentHeader: {
    background: "#fff",
    marginTop: 6,
  },
  commentHeaderTop: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "16px",
    borderBottom: "1px solid #ddd",
  },
  commentHeaderTitle: {
    fontWeight: "bold",
  },
  commentHeaderRatingInfo: {
    display: "flex",
    alignItems: "center",
    gap: 5,
  },
  ratingValue: {
    fontSize: 13,
    fontWeight: 500,
  },
  ratingCount: {
    color: "#C6C6C6",
    fontSize: 13,
    fontWeight: 500,
    lineHeight: 1.5,
  },
  seeAllLink: {
    fontSize: "14px",
    color: "#B8B8B8",
  },
  commentContainer: {
    display: "flex",
    flexDirection: "column",
    gap: 2,
    padding: 16,
    borderTop: "1px solid #ddd",
    width: "100%",
  },
  userInfoContainer: {
    display: "flex",
    alignItems: "start",
    gap: 10,
  },
  avatar: {
    width: 30,
    height: 30,
    marginTop: 8,
  },
  userName: {
    fontWeight: "bold",
    lineHeight: 1.5,
  },
  userRatingInfo: {
    display: "flex",
    alignItems: "start",
    justifyContent: "space-between",
    width: "100%",
  },
  productType: {
    color: "#7D7D7D",
    fontSize: 12,
    fontWeight: 400,
    marginBottom: 10,
  },
  commentTime: {
    color: "#ACACAC",
    fontSize: 12,
    fontWeight: 400,
  },
  commentText: {
    fontSize: 14,
  },
  imagePlaceholder: {
    width: "100%",
    paddingTop: "100%",
    backgroundColor: "#eee",
    border: "1px solid #ddd",
    borderRadius: 1,
  },
  relatedProductsTitle: {
    fontWeight: "bold",
    marginBottom: 10,
  },
  salePriceText: {
    fontSize: 26,
    fontWeight: 700,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 16,
    fontWeight: 500,
    color: "#C7C7C7",
  },
  discountText: {
    paddingBlock: 2,
    paddingInline: 5,
    color: "#FFF",
    fontSize: 12,
    fontWeight: 700,
    borderRadius: 10,
  },
  soldText: {
    color: "#626262",
    fontSize: 11,
    fontWeight: 500,
  },
  nameText: {
    color: "#252525",
    fontSize: 17,
    fontWeight: 500,
  },
  priceContainer: {
    display: "flex",
    alignItems: "baseline",
    gap: 4,
  },
};
