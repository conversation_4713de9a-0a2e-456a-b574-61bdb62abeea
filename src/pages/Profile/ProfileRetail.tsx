import PopUpShareLink from "@/components/UI/PopUpShareLink";
import PopupCommon from "@/components/common/PopupCommon";
import AvtHolderIcon from "@/components/icon/AvtHolderIcon";
import CheckIcon from "@/components/icon/CheckIcon";
import { Copys, Download, Share, ZaloIcon } from "@/constants/IconSvg";
import { StorageKeys } from "@/constants/storageKeys";
import { useCalcCartAfterLogin } from "@/hooks/useCalcCartAfterLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getAffiliateConfig } from "@/redux/slices/affiliation/affiliationSlice";
import { getTotalStatusOrder, setOrderSearchCondition } from "@/redux/slices/order/orderSlice";
import { IOrderSearchCondition } from "@/types/order";
import { copy, getReferLink, mapError, showToast } from "@/utils/common";
import { getItem } from "@/utils/storage";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import EditIcon from "@mui/icons-material/Edit";
import {
  Badge,
  Box,
  Button,
  ButtonBase,
  CircularProgress,
  Grid,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { QRCode } from "zmp-qrcode";
import { saveImageToGallery } from "zmp-sdk";
import PopupAddIconScreenApp from "../../components/UI/PopupAddIconScreenApp";
import WarningIcon from "../../components/icon/WarningIcon";
import LayoutHomePage from "../../components/layout/LayoutHomepage";
import { AppEnv, Platform } from "../../config";
import {
  AccountItemType,
  AccountItems,
  AppLink,
  IAccountItem,
  MyOrderItemItems,
  MyOrderItemType,
  OrderStatusType,
} from "../../constants/Const";
import { LogoutIcon, RightChevron } from "../../constants/IconSvg";
import { Router } from "../../constants/Route";
import { NumOfLine } from "../../constants/Style";
import { COLORS } from "../../constants/themes";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { authZalo, getUser, getUserZalo, logout } from "../../redux/slices/authen/authSlice";
import { AppDispatch, RootState } from "../../redux/store";
import { useNavigate } from "../../utils/component-util";
import { openChat } from "../../utils/openChat";
import AffiliateRegisterSheet from "./components/AffiliateRegisterSheet";
import MembershipSlider from "./components/MembershipSliderRetail";
import PendingApprovalSheet from "./components/PendingApprovalSheet";
import RegisterPartnerSheet from "./components/RegisterPartnerSheet";

export default function Profile() {
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { color, ...appConfig } = useConfigApp();
  const {
    orderSearchCondition,
  }: {
    orderSearchCondition: IOrderSearchCondition;
  } = useSelector((state: RootState) => state.order);
  const { user } = useSelector((state: RootState) => state.auth);
  const { isAffiliationConfigActive, affiliateConfig } = useSelector(
    (state: RootState) => state.affiliation
  );
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { showAlert } = useAlert();
  const [openPopup, setOpenPopup] = useState(false);
  const [orderItems, setOrderItems] = useState(MyOrderItemItems);
  const [openRegisterPartnerSheet, setOpenRegisterPartnerSheet] = useState(false);
  const [openAffiliateRegisterSheet, setOpenAffiliateRegisterSheet] = useState(false);
  const [openPendingSheet, setOpenPendingSheet] = useState(false);

  const [loading, setLoading] = useState(false);
  const [openLoginPopup, setOpenLoginPopup] = useState(false);
  const calcCartAfterLogin = useCalcCartAfterLogin();
  const { container } = useConfigApp();

  const isShowAffiliate = isAffiliationConfigActive || user?.affiliationStatus === "Actived";

  const onClickRegister = async () => {
    setLoading(true);
    const refCode = await getItem(StorageKeys.RefCode);
    const res = await dispatch(
      authZalo(!refCode || refCode === "undefined" ? "" : refCode)
    ).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      await calcCartAfterLogin();
      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: "Kích hoạt tài khoản thành công",
      });
    } else {
      showAlert({
        content: mapError(res.error),
      });
    }
    setLoading(false);
  };

  const fetchOrder = async () => {
    const result = await dispatch(getTotalStatusOrder());

    const data = result.payload?.data || {};

    const quantityMap = {
      [MyOrderItemType.WaitingConfirm]: "pendding",
      [MyOrderItemType.WaitingForDelivery]: "waitingForDelivery",
      [MyOrderItemType.Delivering]: "delivering",
    };

    const updatedItems = MyOrderItemItems.map((item) => {
      const dataKey = quantityMap[item.id];
      const quantity = dataKey ? data[dataKey] || 0 : 0;
      return { ...item, quantity };
    });

    setOrderItems(updatedItems);
  };

  useEffect(() => {
    if (user) {
      fetchOrder();
    }
  }, [user]);

  const onLogout = () => {
    showAlert({
      icon: <WarningIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
      title: "Đăng xuất",
      content: "Bạn chắc chắn muốn đăng xuất chứ?",
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "OK",
          action: () => {
            dispatch(logout());
            navigate(Router.login);
          },
        },
      ],
    });
  };

  const onClickToActiveProfile = () => {
    showAlert({
      title: appConfig.shopName,
      content: "Vui lòng kích hoạt tài khoản để tiếp tục!",
      icon: "⚠️",
      buttons: [
        {
          title: "Đóng",
          action: () => {
            // navigate(Router.profile.activeProfile);
          },
        },
        {
          title: "Kích hoạt",
          action: () => {
            Platform === "zalo" ? onClickRegister() : setOpenLoginPopup(true);
          },
        },
      ],
    });
  };

  const onClickToItem = (item: IAccountItem) => {
    switch (item.id) {
      case AccountItemType.Payment:
        navigate(Router.profile.payment);
        break;
      case AccountItemType.Address:
        if (!user) {
          onClickToActiveProfile();
          return;
        }
        navigate(Router.profile.address);
        break;
      case AccountItemType.Voucher:
        navigate(Router.voucher.index);
        break;
      case AccountItemType.AppIcon:
        setOpenPopup(true);
        break;
      case AccountItemType.Collab:
        navigate(Router.profile.teamList.index);
        break;
      case AccountItemType.ReferCode:
        navigate(Router.refer.index);
        break;
      case AccountItemType.UserInfo:
        if (!user) {
          onClickToActiveProfile();
          return;
        }
        navigate(Router.profile.info);
        break;
      case AccountItemType.Branch:
        navigate(Router.branch.index);
        break;
      case AccountItemType.Policy:
        navigate(Router.profile.policy.index);
        break;
      case AccountItemType.Support:
        openChat(appConfig.oaId || "");
        break;
    }
  };

  const onClickToShortCutItem = (item: IAccountItem) => {
    if (!user) {
      onClickToActiveProfile();
      return;
    }
    switch (item.id) {
      case MyOrderItemType.WaitingConfirm:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Pending,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.WaitingForDelivery:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.WaitingForDelivery,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.Delivering:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Delivering,
          })
        );
        navigate(Router.order.index);
        break;
      case MyOrderItemType.Review:
        dispatch(
          setOrderSearchCondition({
            ...orderSearchCondition,
            orderStatus: OrderStatusType.Success,
          })
        );
        navigate(Router.order.index);
        break;
    }
  };

  const AccountItem = (item: IAccountItem) => {
    const customStyle =
      item.id === AccountItemType.Address || item.id === AccountItemType.Voucher
        ? { height: 65 }
        : {};

    return (
      <ButtonBase
        key={item.id}
        sx={{ ...styles.accountItem, ...customStyle }}
        onClick={() => onClickToItem(item)}
      >
        <Stack direction="row" alignItems="center" gap={1.5}>
          {item.icon()}
          <Stack textAlign={"left"}>
            <Typography fontWeight={500} fontSize="15px">
              {item.title}
            </Typography>
            <Typography sx={NumOfLine(1)} style={{ color: COLORS.neutral4, fontSize: "11px" }}>
              {item.subTitle}
            </Typography>
          </Stack>
        </Stack>
        {!item.isFirst && <RightChevron width={12} height={21} />}
      </ButtonBase>
    );
  };
  const MenuAccountItem = ({ item, isLast }: { item: IAccountItem; isLast: boolean }) => (
    <ButtonBase
      key={item.id}
      sx={{
        ...styles.menuItem,
        borderBottom: !isLast ? "1px solid #ECECEC" : "none",
      }}
      onClick={() => onClickToItem(item)}
    >
      <Stack direction="row" alignItems="center" gap={1.5}>
        {item.icon()}
        <Stack textAlign={"left"}>
          <Typography fontWeight={500} fontSize="15px">
            {item.title}
          </Typography>
          <Typography sx={NumOfLine(1)} style={{ color: COLORS.neutral4, fontSize: "11px" }}>
            {item.subTitle}
          </Typography>
        </Stack>
      </Stack>
      <RightChevron width={12} height={21} />
    </ButtonBase>
  );

  const ShortCutItem = (item: IAccountItem) => (
    <ButtonBase sx={styles.shortCutItem} onClick={() => onClickToShortCutItem(item)}>
      <Stack direction="column" alignItems="center" gap={1}>
        <Badge key={item.id} badgeContent={item.quantity} color="error">
          {item.icon()}
        </Badge>
        <Stack>
          <Typography fontWeight={400} fontSize="11px" color={COLORS.neutral5}>
            {item.title}
          </Typography>
        </Stack>
      </Stack>
    </ButtonBase>
  );

  const MyOrderShortCut = () => {
    return (
      <>
        <Stack
          direction="row"
          justifyContent={"space-between"}
          alignItems={"center"}
          sx={{
            marginBottom: "25px",
          }}
        >
          <Typography
            style={{
              color: color.primary,
              fontSize: "18px",
              fontWeight: 700,
            }}
          >
            Đơn hàng của tôi
          </Typography>
          <ButtonBase
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 0.4,
            }}
            onClick={() => {
              if (!user) {
                onClickToActiveProfile();
                return;
              }
              navigate(Router.order.index);
            }}
          >
            <Typography
              style={{
                color: color.primary,
                fontSize: "11px",
                fontWeight: 400,
              }}
            >
              Xem lịch sử mua hàng
            </Typography>
            <RightChevron width={10} height={10} strokeColor={color.primary} />
          </ButtonBase>
        </Stack>
        <Grid container spacing={1.3}>
          {orderItems.map((item, idx) => (
            <Grid key={item.id} item xs={3}>
              <ShortCutItem {...item} />
            </Grid>
          ))}
        </Grid>
      </>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <Stack style={styles.activeAccount}>
          <CircularProgress />
        </Stack>
      );
    } else
      return (
        <Box>
          {/* {user && (user?.point > 0 || user?.membershipLevel) && (
            <Stack style={styles.profileContainer}>
              <MembershipSlider />
            </Stack>
          )} */}
          <Stack
            style={{
              ...styles.profileContainer,
              padding: "10px 20px 25px ",
              marginTop: "10px",
            }}
          >
            <MyOrderShortCut />
          </Stack>
          {/* Affiliate Partner Program - moved here as a separate Stack */}
          {isShowAffiliate && (
            <Stack style={styles.profileContainer}>
              <Box sx={{ background: "#fff", borderRadius: 2 }}>
                <Typography sx={{ fontWeight: 700, fontSize: 18, color: color.primary, mb: 2 }}>
                  Chương trình đối tác
                </Typography>
                <Box sx={{ border: `1.5px solid ${color.primary}`, borderRadius: 3, p: 2 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      gap: 3,
                    }}
                  >
                    {/* Cột trái chiếm hết chiều ngang, không ép width nhỏ */}
                    <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                      <Box>
                        <Typography
                          sx={{
                            fontSize: 12, // nhỏ hơn nữa nếu vẫn chưa đủ
                            color: "#161616",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            fontWeight: 400,
                          }}
                        >
                          Giới thiệu khách hàng cấp 1 hoa hồng
                        </Typography>
                        <Typography
                          sx={{ fontWeight: 700, fontSize: 18, color: color.primary, mb: 1 }}
                        >
                          {affiliateConfig?.basicCommissionsConfig?.levelOneCommissionPercentage ??
                            0}
                          %
                        </Typography>
                      </Box>
                      {affiliateConfig?.basicCommissionsConfig?.isActiveLevelTwo && (
                        <>
                          <Box sx={{ borderBottom: "1px solid #E0E0E0", width: "100%", my: 0.5 }} />
                          <Box>
                            <Typography
                              sx={{
                                fontSize: 12,
                                color: "#161616",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                fontWeight: 400,
                              }}
                            >
                              Giới thiệu khách hàng cấp 2 hoa hồng
                            </Typography>
                            <Typography
                              sx={{ fontWeight: 700, fontSize: 18, color: color.primary }}
                            >
                              {affiliateConfig?.basicCommissionsConfig
                                ?.levelTwoCommissionPercentage ?? 0}
                              %
                            </Typography>
                          </Box>
                        </>
                      )}
                    </Box>
                    <Button
                      variant="contained"
                      sx={{
                        borderRadius: 99,
                        px: 3,
                        py: 1,
                        fontWeight: 700,
                        fontSize: 14,
                        background: color.primary,
                        color: "#fff",
                        minWidth: 80,
                        flexShrink: 0,
                      }}
                      onClick={() => {
                        if (!user) {
                          onClickToActiveProfile();
                          return;
                        }
                        if (user?.affiliationStatus === "Actived") {
                          navigate(Router.collabhistory.index);
                        } else if (user?.affiliationStatus === "InActived") {
                          setOpenPendingSheet(true);
                        } else {
                          setOpenRegisterPartnerSheet(true);
                        }
                      }}
                    >
                      {user?.affiliationStatus === "Actived" ||
                      user?.affiliationStatus === "InActived"
                        ? "Quản lý"
                        : "Đăng ký"}
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Stack>
          )}
          <Stack style={styles.profileContainer}>
            <div>
              <Grid container spacing={1.3}>
                {AccountItems.First.map((item, idx) => (
                  <Grid key={item.id} item xs={6}>
                    <AccountItem {...item} />
                  </Grid>
                ))}
              </Grid>
            </div>
            <Stack
              style={{
                ...styles.sectionContainer,
                border: "1px solid #ECECEC",
                borderRadius: "5px",
              }}
              mt={"10px"}
            >
              {AccountItems.Second.map((item, idx) => {
                const isLast = idx === AccountItems.Second.length - 1;
                return (
                  <Box key={item.id}>
                    <MenuAccountItem item={item} isLast={isLast} />
                  </Box>
                );
              })}
            </Stack>
            {/* <UserReferCode /> */}
            {Platform === "web" && user && (
              <Stack sx={styles.logoutBtn} direction="row" gap={1} onClick={onLogout}>
                <LogoutIcon />
                <Stack>
                  <span style={{ fontWeight: 700, color: "red" }}>Đăng xuất</span>
                </Stack>
              </Stack>
            )}
            {/* <Box
              sx={{
                color: "#8B8B8B",
                display: "flex",
                alignItems: "center",
                gap: "5px",
                padding: "20px 10px 0 10px",
                justifyContent: "center",
                fontWeight: "500",
                fontSize: "11px",
              }}
            >
              POWERED BY <img height={"15px"} src="demo/logo/logo-evotech.svg" />
            </Box> */}
            <PopupAddIconScreenApp isOpen={openPopup} closePopup={() => setOpenPopup(false)} />
          </Stack>
        </Box>
      );
  };

  const [src, setSrc] = useState("");
  const [isOpenShareLink, setIsOpenShareLink] = useState(false);
  const [referLink, setReferLink] = useState("");

  useEffect(() => {
    if (!user) {
      setReferLink(AppEnv === "production" ? AppLink : `${AppLink}?env=TESTING`);
      return;
    }
    const code = user?.referralCode || "";
    const link = getReferLink(code);
    setReferLink(link);
  }, [user]);

  const handleCopyLink = () => {
    copy(referLink, "link giới thiệu");
  };

  const downloadQRCode = () => {
    if (!src) return;
    const onSuccess = () => {
      showToast({ content: "Tải mã QR thành công", type: "success" });
    };
    const onError = (error) => {
      showToast({ content: "Tải mã QR thất bại", type: "error" });
      console.log(error);
    };
    if (Platform === "web") {
      try {
        const link = document.createElement("a");
        link.href = src.startsWith("data:image") ? src : `data:image/png;base64,${src}`;
        link.download = "qr-code.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        onSuccess();
      } catch (error) {
        onError(error);
      }
    } else {
      saveImageToGallery({
        imageUrl: src,
        success: onSuccess,
        fail: onError,
      });
    }
  };

  const profileTitle =
    Array.isArray(container?.navbar) && container.navbar[4]?.text
      ? container.navbar[4].text
      : "Tài khoản";
  useEffect(() => {
    if (isShowAffiliate && shopId) {
      dispatch(getAffiliateConfig(shopId));
    }
  }, [isShowAffiliate, shopId]);

  return (
    <LayoutHomePage
      title={
        <Typography sx={{ fontWeight: 700, fontSize: "17px", color: "#161616", mb: 0, ml: 3 }}>
          {profileTitle}
        </Typography>
      }
    >
      <Box
        sx={{
          backgroundColor: appConfig.container?.backgroundColor,
        }}
      >
        <Box sx={{ background: appConfig.container?.backgroundColor, minHeight: "100vh" }}>
          <Box sx={{ background: COLORS.white, p: "20px 20px 5px 20px", my: 1 }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
              <Stack direction="row" alignItems="center" gap={1.5} width="90%">
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <AvtHolderIcon fill={color.primary} />
                </Box>
                <Stack sx={{ width: "100%" }}>
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography
                      sx={{ fontWeight: 700, fontSize: 22, color: color.primary, lineHeight: 1 }}
                    >
                      {user?.fullname ?? user?.phoneNumber ?? "Chưa kích hoạt"}
                    </Typography>
                    <ButtonBase
                      onClick={() => {
                        if (!user) {
                          onClickToActiveProfile();
                          return;
                        }
                        navigate(Router.profile.info);
                      }}
                      sx={{
                        p: 0.5,
                        borderRadius: "50%",
                        "&:hover": {
                          backgroundColor: "rgba(0,0,0,0.04)",
                        },
                      }}
                    >
                      <EditIcon
                        sx={{
                          fontSize: 16,
                          color: color.primary,
                          opacity: 0.7,
                        }}
                      />
                    </ButtonBase>
                  </Stack>
                  {user?.membershipLevel?.levelName && (
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: 14,
                        color: color.primary,
                        opacity: 0.85,
                        mt: 0.5,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        width: "95%",
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                        whiteSpace: "normal",
                      }}
                    >
                      {user.membershipLevel.levelName}
                    </Typography>
                  )}
                </Stack>
              </Stack>
            </Stack>
            <MembershipSlider
              key={`${user?.point}-${user?.membershipLevel?.levelName}-${user?.membershipLevel?.logo}`}
            />
          </Box>

          {renderContent()}

          <PopupCommon
            open={openLoginPopup}
            setOpen={setOpenLoginPopup}
            content={
              <>
                <Typography
                  fontSize={22}
                  fontWeight={700}
                  color={COLORS.black}
                  mb={1}
                  align="center"
                >
                  Yêu cầu đăng nhập
                </Typography>
                <Typography
                  fontSize={14}
                  align="center"
                  // color="textSecondary"
                  style={{ color: "#A5A5A5" }}
                  mb={3}
                  px={2}
                >
                  Để sử dụng chức năng này, bạn cần đăng nhập để sử dụng
                </Typography>
                <Box display="flex" flexDirection="column" gap={1}>
                  {Platform == "web" && (
                    <Button
                      variant="contained"
                      startIcon={<AccountCircleIcon />}
                      color="primary"
                      style={{
                        color: color.accent,
                        backgroundColor: color.primary,
                        paddingBlock: 10,
                        height: 60,
                        fontWeight: 500,
                        fontSize: 14,
                      }}
                      fullWidth
                      onClick={() => navigate(`${Router.login}`)}
                    >
                      Đăng nhập với tài khoản
                    </Button>
                  )}
                  {Platform === "zalo" && (
                    <Button
                      variant="outlined"
                      startIcon={<ZaloIcon />}
                      style={{
                        color: color.primary,
                        backgroundColor: color.accent,
                        paddingBlock: 10,
                        height: 60,
                        fontWeight: 500,
                        fontSize: 14,
                      }}
                      fullWidth
                      onClick={onClickRegister}
                    >
                      Đăng nhập với Zalo
                    </Button>
                  )}
                </Box>
                {Platform === "web" && (
                  <Typography align="center" color="textSecondary" fontSize={11} mt={1} mb={3}>
                    Bạn chưa có tài khoản?{" "}
                    <Typography
                      component="span"
                      style={{
                        cursor: "pointer",
                        textDecoration: "underline",
                        color: color.primary,
                      }}
                      onClick={() => navigate(`${Router.register}`)}
                    >
                      Đăng ký
                    </Typography>
                  </Typography>
                )}
              </>
            }
          />
          <RegisterPartnerSheet
            open={openRegisterPartnerSheet}
            onClose={() => setOpenRegisterPartnerSheet(false)}
            onOpen={() => setOpenRegisterPartnerSheet(true)}
            onRegister={() => {
              setOpenRegisterPartnerSheet(false);
              setOpenAffiliateRegisterSheet(true);
            }}
          />
          <AffiliateRegisterSheet
            open={openAffiliateRegisterSheet}
            onClose={() => setOpenAffiliateRegisterSheet(false)}
            onOpen={() => setOpenAffiliateRegisterSheet(true)}
            onPending={() => {
              setOpenAffiliateRegisterSheet(false);
              setOpenPendingSheet(true);
            }}
          />
          <PendingApprovalSheet
            open={openPendingSheet}
            onClose={() => setOpenPendingSheet(false)}
            onOpen={() => setOpenPendingSheet(true)}
          />
          {/* QR Share Section moved here */}
          <Box
            sx={{
              mt: 1,
              background: COLORS.white,
              p: 3,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Typography
              sx={{ fontWeight: 500, fontSize: 15, textAlign: "center", mb: 1, width: "100%" }}
            >
              Chia sẻ cửa hàng
              <span
                style={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  width: "100%",
                  display: "inline-block",
                  verticalAlign: "bottom",
                }}
                title={appConfig.shopName}
              >
                {appConfig.shopName}
              </span>
              với bạn bè để nhận điểm thưởng! QR này có chứa mã giới thiệu của bạn!
            </Typography>
            <Typography
              component="a"
              onClick={() => navigate(Router.profile.earnPointsGuide)}
              sx={{
                color: color.primary,
                fontSize: 14,
                mb: 2,
                textDecoration: "underline",
                cursor: "pointer",
              }}
            >
              Xem chi tiết
            </Typography>
            <Box
              sx={{
                background: "#fff",
                borderRadius: 3,
                p: 2,
                mb: 2,
                border: "1px dashed #E0E0E0",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                width: "100%",
              }}
            >
              <QRCode
                size={180}
                value={referLink}
                ref={(el) => (el ? setTimeout(() => setSrc(el.getBase64()), 1000) : el)}
              />
            </Box>
            <Stack direction="row" spacing={3} justifyContent="center" sx={{ mt: 1 }}>
              <Stack
                alignItems="center"
                minWidth="95px"
                sx={{ cursor: "pointer" }}
                onClick={handleCopyLink}
              >
                <Box
                  sx={{
                    width: 56,
                    height: 56,
                    borderRadius: "50%",
                    background: "#F6F7FB",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mb: 0.5,
                  }}
                >
                  <Copys />
                </Box>
                <Typography sx={{ fontSize: 15, color: color.primary, fontWeight: 500 }}>
                  Sao chép
                </Typography>
              </Stack>
              <Stack
                alignItems="center"
                minWidth="95px"
                sx={{ cursor: "pointer" }}
                onClick={downloadQRCode}
              >
                <Box
                  sx={{
                    width: 56,
                    height: 56,
                    borderRadius: "50%",
                    background: "#F6F7FB",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mb: 0.5,
                  }}
                >
                  <Download />
                </Box>
                <Typography sx={{ fontSize: 15, color: color.primary, fontWeight: 500 }}>
                  Tải xuống
                </Typography>
              </Stack>
              <Stack
                alignItems="center"
                minWidth="95px"
                sx={{ cursor: "pointer" }}
                onClick={() => setIsOpenShareLink(true)}
              >
                <Box
                  sx={{
                    width: 56,
                    height: 56,
                    borderRadius: "50%",
                    background: "#F6F7FB",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mb: 0.5,
                  }}
                >
                  <Share />
                </Box>
                <Typography sx={{ fontSize: 15, color: color.primary, fontWeight: 500 }}>
                  Chia sẻ link
                </Typography>
              </Stack>
            </Stack>
            <PopUpShareLink isOpen={isOpenShareLink} setIsOpen={setIsOpenShareLink} />
          </Box>
        </Box>
        {/* --- QR Share Section End --- */}
        <Box
          sx={{
            width: "100%",
            background: `${appConfig.container?.backgroundColor}`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            py: 1.5,
          }}
        >
          <Typography
            sx={{
              fontSize: 12,
              color: "#BDBDBD",
              textAlign: "center",
              display: "flex",
              alignItems: "center",
              gap: 0.5,
              letterSpacing: 0.5,
              fontWeight: 400,
            }}
          >
            POWERED BY{" "}
            <img src="/demo/logo/logo-evotech.svg" height={15} style={{ marginLeft: 4 }} />
          </Typography>
        </Box>
        {AppEnv === "dev" && (
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={async () => {
              dispatch(logout());
            }}
          >
            Log out
          </Button>
        )}
      </Box>
    </LayoutHomePage>
  );
}

const styles: Record<string, React.CSSProperties> = {
  profileContainer: {
    background: COLORS.white,
    padding: "20px 20px 5px 20px",
    marginBottom: "10px",
  },
  sectionContainer: {
    background: COLORS.white,
    padding: 0,
    borderRadius: 10,
    color: COLORS.black,
  },
  accountItem: {
    width: "100%",
    display: "flex",
    padding: "14px",
    flexDirection: "row",
    gap: "4px",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: "5px",
    border: "1px solid #ECECEC",
  },
  menuItem: {
    width: "100%",
    display: "flex",
    padding: "14px",
    flexDirection: "row",
    gap: "4px",
    justifyContent: "space-between",
    alignItems: "center",
  },
  logoutBtn: {
    fontWeight: 400,
    color: "#000",
    borderRadius: 99,
    background: "#fff",
    padding: "10px 16px",
    justifyContent: "center",
    alignItems: "center",
  },
  shopName: {
    fontWeight: 700,
    fontSize: "16px",
    width: `calc(100vw - 180px)`,
    display: "block",
    whiteSpace: "normal",
    overflow: "hidden",
    textOverflow: "ellipsis",
    lineHeight: 1.3,
  },
  shortCutItem: {
    textAlign: "center",
    width: "100%",
  },
  activeAccount: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    height: 500,
    borderRadius: 20,
  },
};
