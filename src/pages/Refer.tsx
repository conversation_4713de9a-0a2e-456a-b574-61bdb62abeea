import React, { useEffect, useRef, useState } from "react";
import FrameContainer from "../components/layout/Container";
import { <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { AppDispatch, RootState } from "../redux/store";
import { useDispatch, useSelector } from "react-redux";
import { getRouteParams } from "zmp-sdk";
import { joinTeam } from "../redux/slices/team/team";
import {
  authZalo,
  getMyParent,
  getUser,
  getUserZalo,
  updateMe,
} from "../redux/slices/authen/authSlice";

const settings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 3000,
};
import { useTheme } from "@mui/material/styles";
import { mapError, showToast } from "../utils/common";
import { useAlert } from "../redux/slices/alert/useAlert";
import { Box } from "zmp-ui";
import { LoadingButton } from "@mui/lab";
import { Router } from "../constants/Route";
import { useNavigate } from "../utils/component-util";
import Banner from "../components/banner/Banner";
import { COLORS } from "../constants/themes";
import { useShareReferLink } from "../hooks/useShareReferLink";
import CheckIcon from "../components/icon/CheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { Platform } from "@/config";
import { formatPrice } from "@/utils/formatPrice";
import { updateShopVisit } from "@/redux/slices/affiliation/affiliationSlice";

interface IParent {
  id: number;
  name: string;
  referCode: string;
}

export default function ReferPage() {
  const dispatch = useDispatch<AppDispatch>();
  const { color, ...appConfig } = useConfigApp();
  const { user } = useSelector((state: RootState) => state.auth);
  const updatedReferCode = user?.referralCode;
  const { affiliateReport } = useSelector((state: RootState) => state.affiliation);
  const [fParent, setFParent] = useState<IParent>(null as unknown as IParent);
  const { refCode } = getRouteParams();
  const [refCodeTxt, setRefCodeTxt] = useState<string>(refCode);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const hasSentVisitRef = useRef(false);

  const theme = useTheme();
  const { showAlert } = useAlert();
  const { shareLink } = useShareReferLink();

  const getParent = async () => {
    try {
      const res = await dispatch(getMyParent());
      if (res?.payload?.data) {
        setFParent(res.payload.data);
      }
    } catch (e: any) {
      showToast({
        content: e.message ?? "Xuất hiện lỗi trong quá trình lấy dữ liệu",
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (refCode) {
      setRefCodeTxt(refCode);
    }
    if (refCode && user && shopId && !hasSentVisitRef.current) {
      dispatch(updateShopVisit({ shopId, isByLink: true }));
      hasSentVisitRef.current = true;
    }
  }, [refCode, user, shopId]);

  const handleJoinTeam = async () => {
    setLoading(true);
    if (!refCodeTxt) {
      showToast({
        content: "Vui lòng nhập mã giới thiệu!",
        type: "error",
      });
      return;
    }
    try {
      const res: any = await dispatch(updateMe({ referrerCode: refCodeTxt }));
      if (res.error) {
        showAlert({
          content: "Mã giới thiệu không hợp lệ!",
        });
        return;
      } else {
        showAlert({
          icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
          title: "Bạn đã cập nhật mã giới thiệu thành công!",
          buttons: [
            {
              title: "OK",
              action: () => {
                dispatch(getUser());
              },
            },
          ],
        });
      }
    } catch (e: any) {
      console.log("🚀 ~ handleJoinTeam ~ e:", e);
      showToast({
        content: e?.message ?? "Xuất hiện lỗi khi cập nhật mã giới thiệu. Vui lòng thử lại",
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const navigateHome = () => {
    navigate(Router.homepage);
  };

  const handleRegister = async () => {
    if (!refCodeTxt) {
      return showToast({
        content: "Vui lòng nhập mã giới thiệu",
        type: "error",
      });
    }
    setLoading(true);
    const res = await dispatch(authZalo(refCodeTxt)).unwrap();
    if (res && res.idFor) {
      if (Platform == "zalo") await dispatch(getUserZalo());
      await dispatch(getUser());
      showAlert({
        icon: <CheckIcon primaryColor={color.primary} secondaryColor={color.secondary} />,
        title: "Kích hoạt tài khoản thành công",
        buttons: [{ title: "OK", action: navigateHome }],
      });
    } else {
      showAlert({
        content: res.data?.detail,
      });
    }
    setLoading(false);
  };

  /**
   * @description styles
   */
  const titleStyle: React.CSSProperties = {
    fontWeight: 700,
    fontSize: 16,
    marginBottom: 12,
    color: COLORS.primary,
  };
  const buttonContainerStyle: React.CSSProperties = {
    color: "#FFF",
    background: COLORS.primary1,
    borderRadius: 99,
    padding: "4 0",
    width: "100%",
  };
  const inputContainerStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
    background: "#F4F4F4",
    padding: 4,
    borderRadius: "10px",
    marginBottom: 12,
  };
  const titleInputStyle: React.CSSProperties = {
    color: COLORS.primary,
  };
  const referCodeInput: React.CSSProperties = {
    border: "none",
    backgroundColor: "transparent",
    padding: 10,
    color: COLORS.primary1,
    fontWeight: 500,
    fontSize: 16,
    width: "50%",
    outline: "none",
  };
  const codeLabelStyle: React.CSSProperties = {
    color: COLORS.primary1,
    fontWeight: 500,
    fontSize: 16,
    width: 100,
  };

  //----------------------

  /**
   * @description render UI
   */

  const LineUI = () => <Box style={{ width: "1px", height: "38px", background: "#D9D9D9" }} />;

  const renderNotUpdateReferCode = () => (
    <Stack style={{ background: "#fff" }} padding={2} borderRadius={4} marginBottom={1}>
      <Typography style={titleStyle}>
        Kích hoạt tài khoản Đối tác kinh doanh {appConfig?.shopName} để gia tăng thu nhập thụ động
        bằng việc bán hàng online
      </Typography>

      <Box style={inputContainerStyle}>
        <Typography style={titleInputStyle} px={2}>
          Mã giới thiệu
        </Typography>
        <LineUI />
        <input
          placeholder="Nhập mã"
          value={user?.referrerCode || refCodeTxt}
          onChange={(e) => setRefCodeTxt(e.target.value)}
          style={referCodeInput}
          disabled={!!user?.referrerCode}
        />
      </Box>
      <LoadingButton
        size="large"
        style={{
          ...buttonContainerStyle,
          background: user?.referrerCode ? "gray" : color.primary,
          minHeight: "36px",
        }}
        loading={loading}
        disabled={loading || !!user?.referrerCode}
        onClick={user ? handleJoinTeam : handleRegister}
      >
        {loading
          ? ""
          : user
          ? user?.referrerCode
            ? "Đã có mã giới thiệu"
            : "Cập nhật"
          : "Kích hoạt tài khoản"}
      </LoadingButton>
      {user && (
        <Box style={{ paddingTop: 16 }}>
          <Box style={inputContainerStyle}>
            <Typography style={titleInputStyle} px={2}>
              Mã giới thiệu của bạn
            </Typography>
            <LineUI />
            <Typography style={codeLabelStyle}>{user?.referralCode}</Typography>
          </Box>
          <Button
            size="large"
            style={{ ...buttonContainerStyle, background: color?.primary }}
            onClick={shareLink}
          >
            Chia sẻ link giới thiệu
          </Button>
        </Box>
      )}

      {/* {renderRequirement()} */}
    </Stack>
  );

  const renderUpdatedReferCode = () => (
    <Stack style={{ background: "#fff" }} padding={2} borderRadius={4} marginBlock={1}>
      <Typography style={titleStyle}>
        Bạn đã là đối tác kinh doanh của {appConfig?.shopName}, hãy bán hàng và gia tăng thu nhập
        thụ động nào
      </Typography>
      <Box>
        <Box style={inputContainerStyle}>
          <Typography style={titleInputStyle} px={2}>
            Người giới thiệu
          </Typography>
          <LineUI />
          <Typography style={codeLabelStyle}>{fParent?.referCode}</Typography>
        </Box>

        <Box style={inputContainerStyle}>
          <Typography style={titleInputStyle} px={2}>
            Mã giới thiệu của bạn
          </Typography>
          <LineUI />
          <Typography style={codeLabelStyle}>{user?.referCode}</Typography>
        </Box>
        <Button
          size="large"
          style={{ ...buttonContainerStyle, background: color?.primary }}
          onClick={shareLink}
        >
          Chia sẻ link giới thiệu
        </Button>
        {renderRequirement()}
      </Box>
    </Stack>
  );

  const renderRequirement = () => (
    <Stack
      style={{ background: "#EFF7EA", marginTop: 16 }}
      padding={2}
      borderRadius={4}
      marginBlock={1}
    >
      <Typography style={titleStyle} fontSize={16}>
        Điều kiện để trở thành đối tác kinh doanh của {appConfig?.shopName}:
      </Typography>

      <Typography style={{ paddingBottom: 12 }} fontSize={16}>
        Để trở thành đối tác kinh doanh của {appConfig?.shopName}, quý khách cần đạt những điều kiện
        sau:
      </Typography>

      <Typography
        style={{
          fontWeight: 700,
          marginBottom: 12,
          color: COLORS.accent5,
        }}
        fontSize={14}
      >
        1. Có mã giới thiệu
        {affiliateReport?.requireMinSpent && (
          <>
            <br />
            <span>2. Chi tiêu tối thiểu {formatPrice(affiliateReport.requireMinSpent)}</span>
          </>
        )}
      </Typography>
    </Stack>
  );

  return (
    <FrameContainer title="Giới thiệu cộng tác viên" onBackClick={navigateHome}>
      <div
        style={{ marginLeft: -22, marginRight: -22, marginBottom: 10, background: color.secondary }}
        className="refer-container"
      >
        <Banner />
      </div>
      {/* {updatedReferCode && fParent ? renderUpdatedReferCode() : renderNotUpdateReferCode()} */}
      {renderNotUpdateReferCode()}
    </FrameContainer>
  );
}
