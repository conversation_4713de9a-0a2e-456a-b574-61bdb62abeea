import { Box, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ReportCard from "../../components/report/ReportCard";
import { COLORS, commonStyle } from "../../constants/themes";
import { AppDispatch, RootState } from "../../redux/store";
import ProfileHeader from "./components/ProfileHeader";

import FrameContainerFull from "@/components/layout/ContainerFluid";
import PopUpShareLink from "@/components/UI/PopUpShareLink";
import { Copys, Download, Logo, Refresh, Share } from "@/constants/IconSvg";
import { Router } from "@/constants/Route";
import { useConfigApp } from "@/hooks/useConfigApp";
import { getAffiliateReport } from "@/redux/slices/affiliation/affiliationSlice";
import { copy, getReferLink, showToast } from "@/utils/common";
import { formatPrice } from "@/utils/formatPrice";
import "react-datepicker/dist/react-datepicker.css";
import { useNavigate } from "react-router-dom";
import { QRCode } from "zmp-qrcode";
import { saveImageToGallery } from "zmp-sdk";
import { AppEnv, Platform } from "../../config";
import ReferralTierCard from "./components/ReferralTierCard";
import { AppLink } from "@/constants/Const";

export default function CollabHistoryPage() {
  const { color } = useConfigApp();
  const [isOpenShareLink, setIsOpenShareLink] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);
  const { affiliateReport } = useSelector((state: RootState) => state.affiliation);
  const dispatch = useDispatch<AppDispatch>();
  const [src, setSrc] = useState("");
  const [referLink, setReferLink] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      setReferLink(AppEnv === "production" ? AppLink : `${AppLink}?env=TESTING`);
      return;
    }
    const code = user?.referralCode || "";
    const link = getReferLink(code);
    setReferLink(link);
  }, [user]);

  const listDetail = [
    {
      key: "commissionLevelOne",
      show: true,
      title: (
        <Typography sx={styles.title}>
          Giới thiệu khách hàng cấp 1 hoa hồng{" "}
          <span
            style={{
              ...styles.value,
              color: color.primary,
            }}
          >
            {affiliateReport.levelOneCommissionPercentage}%
          </span>
        </Typography>
      ),
      subTitle: (
        <Typography sx={styles.subTitle}>
          Chia sẻ chúng tôi với bạn bè của bạn để nhận hoa hồng mỗi khi họ mua hàng. Bạn sẽ nhận
          được hoa hồng{" "}
          <span
            style={{
              ...styles.value,
              color: color.primary,
            }}
          >
            {affiliateReport.levelOneCommissionPercentage}%
          </span>{" "}
          giá trị đơn hàng
        </Typography>
      ),
    },
    {
      key: "commissionLevelTwo",
      show: affiliateReport.levelTwoCommissionPercentage != null,
      title: (
        <Typography sx={styles.title}>
          Giới thiệu khách hàng cấp 2 hoa hồng{" "}
          <span
            style={{
              ...styles.value,
              color: color.primary,
            }}
          >
            {affiliateReport.levelTwoCommissionPercentage}%
          </span>
        </Typography>
      ),
      subTitle: (
        <Typography sx={styles.subTitle}>
          Khi khách hàng của bạn giới thiệu thêm người, bạn vẫn nhận được{" "}
          <span
            style={{
              ...styles.value,
              color: color.primary,
            }}
          >
            {affiliateReport.levelTwoCommissionPercentage}%
          </span>{" "}
          hoa hồng trên mỗi đơn hàng của họ.
        </Typography>
      ),
    },
  ];

  useEffect(() => {
    if (user) dispatch(getAffiliateReport());
  }, [user]);

  const handleCopyLink = () => {
    const referLink = getReferLink(user?.referralCode);
    copy(referLink, "link giới thiệu");
  };

  const downloadQRCode = () => {
    if (!src) return;
    const onSuccess = () => {
      showToast({ content: "Tải mã QR thành công", type: "success" });
    };
    const onError = (error) => {
      showToast({ content: "Tải mã QR thất bại", type: "error" });
      console.log(error);
    };
    if (Platform === "web") {
      try {
        const link = document.createElement("a");
        console.log(link);

        link.href = src.startsWith("data:image") ? src : `data:image/png;base64,${src}`;
        link.download = "qr-code.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        onSuccess();
      } catch (error) {
        onError(error);
      }
    } else {
      saveImageToGallery({
        imageBase64Data: src,
        success: onSuccess,
        fail: onError,
      });
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "Chia sẻ cửa hàng",
          text: "Hãy ghé thăm cửa hàng của tôi!",
          url: referLink,
        })
        .catch((error) => console.log("Error sharing:", error));
    } else {
      setIsOpenShareLink(true);
    }
  };

  return (
    <FrameContainerFull title="Affiliate Hub">
      <Stack sx={styles.contentContainer}>
        <ProfileHeader />

        <Box
          sx={{
            background: COLORS.white,
            borderRadius: 2,
            p: 2,
            mt: 2,
            boxShadow: "0 2px 12px rgba(0,0,0,0.04)",
          }}
        >
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography
              style={{
                fontSize: 16,
                fontWeight: 700,
                color: color.primary,
              }}
            >
              Báo cáo hoa hồng
            </Typography>
            {/* <Select
              value={month}
              onChange={(e) => setMonth(e.target.value as number)}
              sx={{
                borderRadius: 99,
                minWidth: 100,
                lineHeight: "12px",
                "& .MuiOutlinedInput-notchedOutline": {
                  border: "none",
                },
                background: color.primary,
                color: COLORS.white,
                "& .MuiSvgIcon-root": {
                  color: COLORS.white,
                },
                "& .MuiInputBase-input": {
                  height: "12px",
                  minHeight: "12px",
                },
              }}
              size="small"
            >
              {[...Array(12)].map((_, i) => (
                <MenuItem key={i} value={i + 1}>
                  Tháng {i + 1}
                </MenuItem>
              ))}
            </Select> */}
          </Stack>
          <ReportCard />
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
            sx={{
              borderRadius: "8px",
              mt: 2,
            }}
          >
            <Refresh />
            <Typography sx={{ fontSize: 13, color: COLORS.neutral2 }}>
              Bạn đã có{" "}
              <Typography component="span" sx={{ fontWeight: 700, color: color.primary }}>
                {formatPrice(affiliateReport.pendingCommission || 0)}
              </Typography>{" "}
              đang chờ duyệt, chia sẻ để kiếm thêm!
            </Typography>
          </Stack>
        </Box>

        <Box sx={{ paddingInline: 2 }}>
          {listDetail
            .filter((item) => item.show)
            .map((item, index) => (
              <ReferralTierCard
                key={index}
                title={item.title}
                subTitle={item.subTitle}
                onShare={() => setIsOpenShareLink(true)}
              />
            ))}
        </Box>
        <PopUpShareLink
          isOpen={isOpenShareLink}
          setIsOpen={(e) => {
            setIsOpenShareLink(e);
          }}
        />
        <Box
          sx={{
            mt: 2,
            mb: 2,
            background: COLORS.white,
            borderRadius: 3,
            boxShadow: "0 2px 12px rgba(0,0,0,0.04)",
            p: 3,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Typography sx={{ fontWeight: 500, fontSize: 15, textAlign: "center", mb: 1 }}>
            Chia sẻ cửa hàng với bạn bè để nhận điểm thưởng! QR này có chứa mã giới thiệu của bạn!
          </Typography>
          <Typography
            component="a"
            onClick={() => navigate(Router.profile.pointHistory)}
            sx={{ color: color.primary, fontSize: 14, mb: 2, textDecoration: "underline" }}
          >
            Xem chi tiết
          </Typography>
          <Box
            sx={{
              background: "#fff",
              borderRadius: 3,
              p: 2,
              mb: 2,
              border: "1px dashed #E0E0E0",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            <QRCode
              size={256}
              value={referLink}
              ref={(el) => (el ? setTimeout(() => setSrc(el.getBase64()), 1000) : el)}
            />
          </Box>
          <Stack direction="row" spacing={3} justifyContent="center" sx={{ mt: 1 }}>
            <Stack
              style={{ ...styles.commissionStepGroup }}
              onClick={handleCopyLink}
              sx={{ cursor: "pointer" }}
            >
              <Stack
                style={{
                  ...styles.iconBgrd,
                }}
              >
                <Copys />
              </Stack>
              <Typography
                style={{
                  ...styles.text,
                  ...styles.label,
                }}
              >
                Sao chép
              </Typography>
            </Stack>
            <Stack
              style={{ ...styles.commissionStepGroup }}
              onClick={downloadQRCode}
              sx={{ cursor: "pointer" }}
            >
              <Stack
                style={{
                  ...styles.iconBgrd,
                }}
              >
                <Download />
              </Stack>
              <Typography
                style={{
                  ...styles.text,
                  ...styles.label,
                }}
              >
                Tải xuống
              </Typography>
            </Stack>
            <Stack
              style={{ ...styles.commissionStepGroup }}
              onClick={() => setIsOpenShareLink(true)}
              sx={{ cursor: "pointer" }}
            >
              <Stack
                style={{
                  ...styles.iconBgrd,
                }}
              >
                <Share />
              </Stack>
              <Typography
                style={{
                  ...styles.text,
                  ...styles.label,
                }}
              >
                Chia sẻ link
              </Typography>
            </Stack>
          </Stack>
          <Typography
            sx={{
              fontSize: 12,
              color: "#BDBDBD",
              mt: 3,
              textAlign: "center",
              display: "flex",
              alignItems: "center",
              gap: 0.5,
            }}
          >
            POWERED BY <Logo />
          </Typography>
        </Box>
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  contentContainer: {
    paddingBottom: 4,
  },
  value: { fontWeight: 700 },
  title: {
    ...commonStyle.headline5,
    color: COLORS.neutral1,
    lineHeight: "25px",
    fontWeight: 700,
  },
  subTitle: {
    fontSize: 14,
    color: COLORS.neutral3,
  },
  commissionStepGroup: {
    alignItems: "center",
    gap: 12,
    minWidth: "95px",
  },
  iconBgrd: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: "100%",
    background: "#F5F5F5",
  },
};
