import { useConfigApp } from "@/hooks/useConfigApp";
import { Box } from "@mui/material";
import React from "react";
import { openShareSheet } from "zmp-sdk/apis";
import { COLORS } from "../../../constants/themes";
import ShareOutLineIcon from "../../icon/ShareOutLineIcon";

interface IShareView {
  title: string;
  description: string;
  thumbnail: string;
  path?: string;
  overrideStyles?: React.CSSProperties;
}

export default function ShareViewV2({
  title,
  description,
  thumbnail,
  path,
  overrideStyles,
}: IShareView) {
  const { color } = useConfigApp();
  const onShareCurrentPage = async () => {
    try {
      let sharedObj = {
        title,
        description,
        thumbnail,
      };
      if (path) {
        sharedObj = Object.assign(sharedObj, { path });
      }

      const data = await openShareSheet({
        type: "zmp_deep_link",
        data: sharedObj,
      });
    } catch (err) {
      console.log("onShareCurrentPage error", err);
    }
  };

  return (
    <Box sx={{ ...styles.shareContainer, ...overrideStyles }} onClick={onShareCurrentPage}>
      <ShareOutLineIcon fillColor={color.primary} />
    </Box>
  );
}

const styles: Record<string, React.CSSProperties> = {
  shareContainer: {
    // width: "45px",
  },
  shareContent: {
    display: "flex",
    alignItems: "center",
    width: "100%",
  },
  shareText: {
    fontSize: 16,
    paddingLeft: 8,
    fontWeight: 700,
    color: COLORS.primary1,
  },
};
